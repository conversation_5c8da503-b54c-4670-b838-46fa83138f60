package com.ruoyi.vo.basicData;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * mes物料批次明细
 */
@Data
public class DocumentInventoryDetailDto {

    private String id;

    /**
     * 出入库单据编码
     */
    private String documentCode;

    /**
     * 出入库单据业务类型
     * 1:生产领料 2:生产补料 3:生产入库（成品入库） 4:生产退料 5采购入库  6采购退货出库  7销售出库  8销售退货入库
     */
    private String businessType;

    private String detailCode;

    private String inventoryId;

    private String batchCode;

    private String location;

    private String materialCode;

    private String specifications;

    /**
     * 单据类型：0-入库，1-出库
     */
    private Integer transactionType;

    private Integer quantity;

    private String remark;

    private Integer completedNum;

    /**
     * 是否让步接收：0-否，1-是
     */
    private Integer isConcession;

    /**
     * 质检状态：
     * 0-无需质检、1-待质检、2-质检中、3-质检合格、4-质检不合格、5-免检、6-让步接收
     */
    private Integer qcStatus;

    /**
     * 出入库状态：
     * 0-待确认、1-待出入库、2-出入库中、3-待生产确认、4-生产已确认、5-待仓库确认、6-仓库已确认、7-已完成
     */
    private Integer warehouseStatus;

    private String containerCode;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 供应商/客户名称
     */
    private String supplierCustomerName;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 位置名称（库位）
     */
    private String positionName;

    /**
     * 层级名称
     */
    private String levelName;

    /**
     * 货架名称
     */
    private String shelfName;

    /**
     * 完成时间（出入库完成时间）
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date completedTime;
}