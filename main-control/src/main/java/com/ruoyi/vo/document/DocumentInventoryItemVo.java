package com.ruoyi.vo.document;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

/**
 * 单据批次确认明细VO
 * 
 * <AUTHOR>
 */
@Data
public class DocumentInventoryItemVo {
    
    /**
     * DocumentInventoryDetail记录的ID
     */
    @NotBlank(message = "批次记录ID不能为空")
    private String documentInventoryDetailId;
    
    /**
     * 出入库数量
     */
    @NotNull(message = "出入库数量不能为空")
    @Positive(message = "出入库数量必须大于0")
    private Integer confirmQuantity;
    
    /**
     * 容器编码
     * 入库场景：用户选择的目标容器（必填）
     * 出库场景：可为空，从数据库记录中获取
     */
    private String containerCode;

}
