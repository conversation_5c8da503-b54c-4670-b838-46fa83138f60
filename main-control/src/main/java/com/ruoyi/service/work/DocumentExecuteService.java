package com.ruoyi.service.work;

import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.domain.basicData.*;
import com.ruoyi.mapper.basicData.BasicDocumentDetailMapper;
import com.ruoyi.mapper.basicData.BasicDocumentInfoMapper;
import com.ruoyi.mapper.basicData.BasicMaterialBatchInventoryMapper;
import com.ruoyi.mapper.basicData.BasicWarehouseContainerMapper;
import com.ruoyi.service.basicData.BasicDocumentInfoService;
import com.ruoyi.service.basicData.BasicMaterialBatchInventoryService;
import com.ruoyi.service.basicData.DocumentInventoryDetailService;
import com.ruoyi.service.erp.ErpReportService;
import com.ruoyi.utils.DateAndTimeUtil;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.ResultMsg;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.document.*;
import com.ruoyi.vo.lk.LkSendTaskDetail;
import com.ruoyi.vo.lk.LkSendTaskRequest;
import com.ruoyi.vo.lk.LkTaskMaterial;
import com.ruoyi.vo.mes.MesDocumentMaterial;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DocumentExecuteService {
    @Resource
    BasicDocumentInfoMapper basicDocumentInfoMapper;

    @Resource
    BasicDocumentDetailMapper basicDocumentDetailMapper;

    @Resource
    DocumentInventoryDetailService documentInventoryDetailService;

    @Resource
    BasicWarehouseContainerMapper basicWarehouseContainerMapper;

    @Resource
    BasicMaterialBatchInventoryMapper materialBatchInventoryMapper;

    @Resource
    BasicMaterialBatchInventoryService basicMaterialBatchInventoryService;

    @Resource
    LkSystemService lkSystemService;

    @Resource
    MesUpperService mesUpperService;

    @Resource
    RecordMaterialInoutService recordMaterialInoutService;

    @Resource
    ErpReportService erpReportService;

    @Resource
    BasicDocumentInfoService basicDocumentInfoService;


    private void executeDocumentStatus(List<DocumentDetailVo> lists) {
        for (DocumentDetailVo documentDetail : lists) {
            BasicDocumentInfo basicDocumentInfo = basicDocumentInfoMapper.getDocumentInfoByCode(documentDetail.getDocumentCode());
            //查询明细
            List<BasicDocumentDetail> basicDocumentDetails = basicDocumentDetailMapper.selectByDocumentCode(basicDocumentInfo.getId());
            Boolean flag = true;
            for (BasicDocumentDetail basicDocumentDetail : basicDocumentDetails) {
                if (basicDocumentDetail.getQuantity() != basicDocumentDetail.getCompletedNum()) {
                    flag = false;
                }
            }
            if (flag) {
                basicDocumentInfo.setStatus(CommonConstant.DocumentStatus.FINISH);
                basicDocumentInfoMapper.updateById(basicDocumentInfo);
            }
        }

    }

    /**
     * 单据出入库确认（新版本）
     * description: 确认本次出入库的实际数量
     * @author: sqpeng
     * @param param 出入库确认请求
     * @return 操作结果
     */
    @Transactional
    public ResponseResult documentInventoryConfirm(DocumentInventoryConfirmVo param) {
        Integer type = param.getType();
        List<DocumentInventoryItemVo> items = param.getItems();
        // 验证数据有效性
        ResponseResult validationResult = validateConfirmItems(items, type);
        if (!validationResult.getCode().equals(ResultMsg.successCode)) {
            return validationResult;
        }
        // 按容器类型分组处理
        List<DocumentInventoryItemVo> boxLkItems = new ArrayList<>();
        List<DocumentInventoryItemVo> plateLkItems = new ArrayList<>();
        List<DocumentInventoryItemVo> profileLkItems = new ArrayList<>();
        List<DocumentInventoryItemVo> withoutLkItems = new ArrayList<>();
        List<RecordMaterialInout> inOutList = new ArrayList<>();
        for (DocumentInventoryItemVo item : items) {
            // 获取批次记录
            DocumentInventoryDetail detail = documentInventoryDetailService.getById(item.getDocumentInventoryDetailId());
            if (detail == null) {
                return ResponseResult.getErrorResult("批次记录不存在，ID: " + item.getDocumentInventoryDetailId());
            }
            // 获取容器编码
            String containerCode = getContainerCodeForConfirm(item, detail, type);
            if (containerCode == null) {
                return ResponseResult.getErrorResult("无法获取容器编码，批次ID: " + item.getDocumentInventoryDetailId());
            }
            // 按容器类型分类
            if (containerCode.contains(CommonConstant.LkName.BOX)) {
                boxLkItems.add(item);
            } else if (containerCode.contains(CommonConstant.LkName.PLATE)) {
                plateLkItems.add(item);
            } else if (containerCode.contains(CommonConstant.LkName.PROFILE)) {
                profileLkItems.add(item);
            } else if (!containerCode.contains("立库")) {
                withoutLkItems.add(item);
            }

            // 创建出入库记录
            createInOutRecord(item, detail, containerCode, inOutList);
        }

        // 分别处理不同类型的容器
        if (!withoutLkItems.isEmpty()) {
            executeWithoutLkConfirm(withoutLkItems, type);
        }
        if (!boxLkItems.isEmpty()) {
            executeLkConfirm(boxLkItems, CommonConstant.LkName.BOX, type);
        }
        if (!plateLkItems.isEmpty()) {
            executeLkConfirm(plateLkItems, CommonConstant.LkName.PLATE, type);
        }
        if (!profileLkItems.isEmpty()) {
            executeLkConfirm(profileLkItems, CommonConstant.LkName.PROFILE, type);
        }

        // 保存出入库记录
        recordMaterialInoutService.addBatchInout(inOutList);
        // 更新单据状态
        updateDocumentStatusByItems(items);
        // 上报ERP
        reportToErpByItems(items, type);
        return ResponseResult.getSuccessResult();
    }

    /**
     * 单据出入库（旧版本，保持兼容性）
     * @deprecated 请使用新版本的documentInventoryConfirm方法
     */
    @Transactional
    public ResponseResult documentInventoryInOut(DocumentInventoryVo param) {
        List<DocumentDetailVo> lists = param.getLists();
        Integer type = param.getType();

        // 验证单据数量逻辑
        ResponseResult validationResult = validateDocumentQuantities(lists);
        if (!validationResult.getCode().equals(ResultMsg.successCode)) {
            return validationResult;
        }
        //区分立库与平面库
        List<DocumentDetailVo> boxLk = new ArrayList<>();
        List<DocumentDetailVo> plateLk = new ArrayList<>();
        List<DocumentDetailVo> profileLk = new ArrayList<>();
        List<DocumentDetailVo> withoutLiKu = new ArrayList<>();
        //出入库list记录
        List<RecordMaterialInout> inOutList  = new ArrayList<>();
        for (DocumentDetailVo documentDetail : lists) {
            List<DocumentInventoryDetailVo> details = documentDetail.getDetails();
            // 过滤出不同类型的立库
            List<DocumentInventoryDetailVo> boxLkDetails = details.stream()
                    .filter(detail -> detail.getContainerCode() != null && detail.getContainerCode().contains(CommonConstant.LkName.BOX))
                    .collect(Collectors.toList());

            List<DocumentInventoryDetailVo> plateLkDetails = details.stream()
                    .filter(detail -> detail.getContainerCode() != null && detail.getContainerCode().contains(CommonConstant.LkName.PLATE))
                    .collect(Collectors.toList());

            List<DocumentInventoryDetailVo> profileLkDetails = details.stream()
                    .filter(detail -> detail.getContainerCode() != null && detail.getContainerCode().contains(CommonConstant.LkName.PROFILE))
                    .collect(Collectors.toList());

            List<DocumentInventoryDetailVo> withoutLiKuDetails = details.stream()
                    .filter(detail -> detail.getContainerCode() != null && !detail.getContainerCode().contains("立库"))
                    .collect(Collectors.toList());
            //包含立库
            if (!boxLkDetails.isEmpty()) {
                documentDetail.setDetails(boxLkDetails);
                boxLk.add(documentDetail);
            }

            if (!plateLkDetails.isEmpty()) {
                documentDetail.setDetails(plateLkDetails);
                plateLk.add(documentDetail);
            }

            if (!profileLkDetails.isEmpty()) {
                documentDetail.setDetails(profileLkDetails);
                profileLk.add(documentDetail);
            }
            //不含立库
            if (!withoutLiKuDetails.isEmpty()) {
                documentDetail.setDetails(withoutLiKuDetails);
                withoutLiKu.add(documentDetail);
            }
            //增加出入库明细
            for (DocumentInventoryDetailVo documentInventoryDetailVo : details) {
                BasicDocumentInfo basicDocumentInfo = basicDocumentInfoMapper.getDocumentInfoByCode(documentDetail.getDocumentCode());
                RecordMaterialInout recordMaterialInout = new RecordMaterialInout();
                recordMaterialInout.setId(UUID.randomUUID().toString());
                recordMaterialInout.setInoutType(basicDocumentInfo.getTransactionType());
                recordMaterialInout.setDataOrigin(basicDocumentInfo.getBusinessSource());
                recordMaterialInout.setBoundType(basicDocumentInfo.getBusinessType());
                recordMaterialInout.setMaterialCode(documentDetail.getMaterialCode());
                recordMaterialInout.setTotalNum(documentInventoryDetailVo.getQuantity());
                recordMaterialInout.setBoundIndex(basicDocumentInfo.getTransactionCode());
                recordMaterialInout.setRecordDate(DateAndTimeUtil.getNowDate());
                recordMaterialInout.setContainerCode(documentInventoryDetailVo.getContainerCode());
                recordMaterialInout.setRecorder(SecurityUtils.getUsername());
                recordMaterialInout.setLockTime(DateAndTimeUtil.getNowDate());
                recordMaterialInout.setUpperIndex(basicDocumentInfo.getSourceDocumentNo());
                inOutList.add(recordMaterialInout);
            }
        }
        //wms仓库
        this.executeWithoutLkTask(withoutLiKu);
        //立库
        if (!boxLk.isEmpty()) {
            this.executeLkTask(boxLk, CommonConstant.LkName.BOX, type);
        }
        if (!plateLk.isEmpty()) {
            this.executeLkTask(plateLk, CommonConstant.LkName.PLATE, type);
        }
        if (!profileLk.isEmpty()) {
            this.executeLkTask(profileLk, CommonConstant.LkName.PROFILE, type);
        }
        //处理单据状态
        this.executeDocumentStatus(lists);
        //增加出入库明细
        recordMaterialInoutService.addBatchInout(inOutList);
        //上报ERP（只有ERP来源的单据才上报，生产相关的除外）
        List<DocumentDetailVo> erpSourceLists = filterErpSourceDocuments(lists);
        if (!erpSourceLists.isEmpty()) {
            erpReportService.reportCurrentOperation(erpSourceLists, type);
        }
        return ResponseResult.getSuccessResult();
    }

    /**
     * 过滤出ERP来源的单据
     *
     * @param lists 单据明细列表
     * @return ERP来源的单据明细列表
     */
    private List<DocumentDetailVo> filterErpSourceDocuments(List<DocumentDetailVo> lists) {
        List<DocumentDetailVo> erpSourceLists = new ArrayList<>();

        for (DocumentDetailVo detail : lists) {
            try {
                BasicDocumentInfo documentInfo = basicDocumentInfoMapper.getDocumentInfoByCode(detail.getDocumentCode());
                if (documentInfo != null && CommonConstant.BusinessSource.ERP == documentInfo.getBusinessSource()) {
                    erpSourceLists.add(detail);
                }
            } catch (Exception e) {
                log.error("检查单据来源失败，单据编码：{}，错误：{}", detail.getDocumentCode(), e.getMessage(), e);
            }
        }

        return erpSourceLists;
    }

    /**
     * 立库数据处理
     */
    private ResponseResult executeLkTask(List<DocumentDetailVo> withLiKu,String lkName,Integer type) {
        LkSendTaskRequest lkSendTaskRequest = new LkSendTaskRequest();
        lkSendTaskRequest.setId(UUID.randomUUID().toString());
        lkSendTaskRequest.setTimestamp(new Date());
        List<LkSendTaskDetail> data = new ArrayList<>();
        for (DocumentDetailVo documentDetailVo : withLiKu){
            LkSendTaskDetail lkSendTaskDetail = new LkSendTaskDetail();
            String taskNo = this.getLkTaskNo(documentDetailVo.getDocumentCode());
            lkSendTaskDetail.setTaskNo(taskNo);
            lkSendTaskDetail.setSource("WMS");
            List<LkTaskMaterial> materielInfos = new ArrayList<>();
            for (DocumentInventoryDetailVo documentInventoryDetailVo : documentDetailVo.getDetails()){
                LkTaskMaterial lkTaskMaterial = new LkTaskMaterial();
                lkTaskMaterial.setMaterielCode(documentDetailVo.getMaterialCode());
                lkTaskMaterial.setQuantity(documentInventoryDetailVo.getQuantity());
                materielInfos.add(lkTaskMaterial);
                //增加明细，修改数量
                DocumentInventoryDetail documentInventoryDetail = new DocumentInventoryDetail();
                BeanUtils.copyProperties(documentDetailVo,documentInventoryDetail);
                documentInventoryDetail.setDetailCode(documentDetailVo.getId());
                this.documentInventoryDetailService.saveObjData(documentInventoryDetail);
            }
            lkSendTaskDetail.setMaterielInfos(materielInfos);
            data.add(lkSendTaskDetail);
        }
        lkSendTaskRequest.setData(data);
        if (type == CommonConstant.InoutType.IN){
            ResponseResult responseResult = lkSystemService.sendLkInTask(lkSendTaskRequest, lkName);
            if (responseResult != null && responseResult.getCode().equals(ResultMsg.successCode)){
                for (DocumentDetailVo documentDetailVo : withLiKu){
                    BasicDocumentDetail basicDocumentDetail = basicDocumentDetailMapper.selectById(documentDetailVo.getId());
                    // 只更新已完成数量，不保存临时的 currentNum
                    basicDocumentDetail.setCompletedNum(basicDocumentDetail.getCompletedNum() + documentDetailVo.getCurrentNum());
                    basicDocumentDetailMapper.updateById(basicDocumentDetail);
                    List<DocumentInventoryDetailVo> details = documentDetailVo.getDetails();
                    for (DocumentInventoryDetailVo documentInventoryDetailVo : details) {
                        // 更新现有的DocumentInventoryDetail记录
                        DocumentInventoryDetail existingDetail = documentInventoryDetailService.getById(documentInventoryDetailVo.getId());
                        if (existingDetail != null) {
                            // 立库入库时需要更新容器信息（用户选择的目标容器）
                            if (type == CommonConstant.InoutType.IN) {
                                existingDetail.setContainerCode(documentInventoryDetailVo.getContainerCode());
                            }
                            existingDetail.setCompletedNum(existingDetail.getCompletedNum() + documentInventoryDetailVo.getQuantity());
                            // 检查批次是否完全完成，如果是则更新状态为已完成
                            if (existingDetail.getCompletedNum().equals(existingDetail.getQuantity())) {
                                existingDetail.setWarehouseStatus(CommonConstant.WarehouseStatus.COMPLETED);
                                existingDetail.setCompletedTime(new Date());
                            }
                            documentInventoryDetailService.updateById(existingDetail);
                        }
                    }
                }
            }
            return responseResult;
        } else {
            ResponseResult responseResult = lkSystemService.sendLkOutTask(lkSendTaskRequest, lkName);
            if (responseResult != null && responseResult.getCode().equals(ResultMsg.successCode)) {
                for (DocumentDetailVo documentDetailVo : withLiKu) {
                    BasicDocumentDetail basicDocumentDetail = basicDocumentDetailMapper.selectById(documentDetailVo.getId());
                    // 只更新已完成数量，不保存临时的 currentNum
                    basicDocumentDetail.setCompletedNum(basicDocumentDetail.getCompletedNum() + documentDetailVo.getCurrentNum());
                    basicDocumentDetailMapper.updateById(basicDocumentDetail);
                    List<DocumentInventoryDetailVo> details = documentDetailVo.getDetails();
                    for (DocumentInventoryDetailVo documentInventoryDetailVo : details) {
                        DocumentInventoryDetail documentInventoryDetail = documentInventoryDetailService.getById(documentInventoryDetailVo.getId());
                        documentInventoryDetail.setCompletedNum(documentInventoryDetail.getCompletedNum() + documentInventoryDetailVo.getQuantity());

                        // 检查批次是否完全完成，如果是则更新状态为已完成
                        if (documentInventoryDetail.getCompletedNum().equals(documentInventoryDetail.getQuantity())) {
                            documentInventoryDetail.setWarehouseStatus(CommonConstant.WarehouseStatus.COMPLETED);
                            documentInventoryDetail.setCompletedTime(new Date());
                        }

                        documentInventoryDetailService.updateById(documentInventoryDetail);
                    }

                    // 更新最新批次状态
                    basicDocumentInfoService.updateLatestBatchStatus(documentDetailVo.getId());
                }
            }
            return responseResult;
        }
    }

    public String getLkTaskNo(String baseCode) {
        // 1. 获取当前时间戳（精确到毫秒）
        String timestamp = String.valueOf(System.currentTimeMillis());
        // 3. 组合生成任务号
        return String.format("%s-%s", baseCode, timestamp);
    }

    /**
     * 非立库数据处理
     */
    private ResponseResult executeWithoutLkTask(List<DocumentDetailVo> withLiKu) {
//        MesDocumentReport mesDocumentReport = new MesDocumentReport();
//        List<DocumentReportDetail> documents = new ArrayList<>();
        for (DocumentDetailVo documentDetailVo : withLiKu) {
//            DocumentReportDetail documentReportDetail = new DocumentReportDetail();
            BasicDocumentInfo basicDocumentInfo = basicDocumentInfoMapper.getDocumentInfoByCode(documentDetailVo.getDocumentCode());
//            //保存报工数据
//            documentReportDetail.setTransactionCode(basicDocumentInfo.getTransactionCode());
//            documentReportDetail.setBusinessType(basicDocumentInfo.getBusinessType());
            List<MesDocumentMaterial> materialDetails = new ArrayList<>();

            List<DocumentInventoryDetailVo> details = documentDetailVo.getDetails();
            for (DocumentInventoryDetailVo documentInventoryDetailVo : details) {
                //单据入库详情新增
                if (basicDocumentInfo.getTransactionType() == CommonConstant.InoutType.IN) {
                    // 入库：使用前端传递的容器编码（用户选择的目标容器）
                    String targetContainerCode = documentInventoryDetailVo.getContainerCode();
                    BasicWarehouseContainer basicWarehouseContainer = basicWarehouseContainerMapper.getContainerByCode(targetContainerCode);
                    if (basicWarehouseContainer != null) {
                        //容器库位,新增批次
                        BasicMaterialBatchInventory basicMaterialBatchInventory = new BasicMaterialBatchInventory();
                        basicMaterialBatchInventory.setId(UUID.randomUUID().toString());

                        // 获取DocumentInventoryDetail记录以获取批次号
                        DocumentInventoryDetail existingDetail = documentInventoryDetailService.getById(documentInventoryDetailVo.getId());
                        // 使用DocumentInventoryDetail中的批次号
                        basicMaterialBatchInventory.setBatch(existingDetail != null ? existingDetail.getBatchCode() : "默认批次");
                        basicMaterialBatchInventory.setMaterialCode(documentDetailVo.getMaterialCode());
                        basicMaterialBatchInventory.setAvailNum(documentInventoryDetailVo.getQuantity());
                        basicMaterialBatchInventory.setContainerCode(targetContainerCode);
                        basicMaterialBatchInventory.setFreezeNum(0);
                        basicMaterialBatchInventory.setCreateTime(new Date());
                        basicMaterialBatchInventory.setProduceDate(new Date());
                        basicMaterialBatchInventory.setInDate(new Date());
                        // upperIndex应该是documentCode（出入库单据编码）
                        basicMaterialBatchInventory.setUpperIndex(existingDetail != null ? existingDetail.getDocumentCode() : documentDetailVo.getDocumentCode());
                        basicMaterialBatchInventory.setMaterialNum(documentInventoryDetailVo.getQuantity());
                        materialBatchInventoryMapper.insert(basicMaterialBatchInventory);

                        // 更新现有的DocumentInventoryDetail记录（已在上面获取）
                        if (existingDetail != null) {
                            // 入库时需要更新容器信息（用户选择的目标容器）
                            existingDetail.setContainerCode(targetContainerCode);
                            existingDetail.setCompletedNum(existingDetail.getCompletedNum() + documentInventoryDetailVo.getQuantity());
                            // 检查批次是否完全完成，如果是则更新状态为已完成
                            if (existingDetail.getCompletedNum().equals(existingDetail.getQuantity())) {
                                existingDetail.setWarehouseStatus(CommonConstant.WarehouseStatus.COMPLETED);
                                existingDetail.setCompletedTime(new Date());
                            }
                            documentInventoryDetailService.updateById(existingDetail);
                        }
                    }
                }
                //单据出库详情
                if (basicDocumentInfo.getTransactionType() == CommonConstant.InoutType.OUT) {
                    DocumentInventoryDetail documentInventoryDetail = documentInventoryDetailService.getById(documentInventoryDetailVo.getId());
                    // 通过容器编码、物料编码和批次号查询批次库存
                    BasicMaterialBatchInventory basicMaterialBatchInventory = basicMaterialBatchInventoryService.getMaterialBatchInventory(
                            documentInventoryDetail.getContainerCode(), documentInventoryDetail.getBatchCode(), documentInventoryDetail.getMaterialCode(), null);
                    if (basicMaterialBatchInventory != null) {
                        basicMaterialBatchInventory.setFreezeNum(basicMaterialBatchInventory.getFreezeNum() - documentInventoryDetailVo.getQuantity());
                        basicMaterialBatchInventory.setMaterialNum(basicMaterialBatchInventory.getMaterialNum() - documentInventoryDetailVo.getQuantity());
                        basicMaterialBatchInventory.setAvailNum(basicMaterialBatchInventory.getMaterialNum() - basicMaterialBatchInventory.getFreezeNum());
                        materialBatchInventoryMapper.updateById(basicMaterialBatchInventory);
                        //库存数量为0删除该批次库存
                        if (basicMaterialBatchInventory.getMaterialNum() == 0 && basicMaterialBatchInventory.getAvailNum() == 0) {
                            materialBatchInventoryMapper.deleteById(basicMaterialBatchInventory.getId());
                        }
                    }
                    // 更新 document_inventory_detail 表的已完成数量
                    if (documentInventoryDetail.getCompletedNum() == null) {
                        documentInventoryDetail.setCompletedNum(0);
                    }
                    documentInventoryDetail.setCompletedNum(documentInventoryDetail.getCompletedNum() + documentInventoryDetailVo.getQuantity());

                    // 检查批次是否完全完成，如果是则更新状态为已完成
                    if (documentInventoryDetail.getCompletedNum().equals(documentInventoryDetail.getQuantity())) {
                        documentInventoryDetail.setWarehouseStatus(CommonConstant.WarehouseStatus.COMPLETED);
                        documentInventoryDetail.setCompletedTime(new Date());
                    }

                    documentInventoryDetailService.updateById(documentInventoryDetail);
                }
            }
            BasicDocumentDetail basicDocumentDetail = basicDocumentDetailMapper.selectById(documentDetailVo.getId());
            // 只更新已完成数量，不保存临时的 currentNum
            basicDocumentDetail.setCompletedNum(basicDocumentDetail.getCompletedNum() + documentDetailVo.getCurrentNum());
            basicDocumentDetailMapper.updateById(basicDocumentDetail);
            // 更新最新批次状态
            basicDocumentInfoService.updateLatestBatchStatus(documentDetailVo.getId());
//            //报工明细保存
//            MesDocumentMaterial mesDocumentMaterial = new MesDocumentMaterial();
//            mesDocumentMaterial.setMaterialCode(documentDetailVo.getMaterialCode());
//            mesDocumentMaterial.setQuantity(documentDetailVo.getCurrentNum());
//            materialDetails.add(mesDocumentMaterial);
//            documentReportDetail.setMaterialDetails(materialDetails);
//            documents.add(documentReportDetail);
        }
//        mesDocumentReport.setDocuments(documents);
        //报工MES
//        mesUpperService.reportDocument(mesDocumentReport);
        return ResponseResult.getSuccessResult();
    }

    /**
     * 验证单据数量逻辑
     * @param lists 单据明细列表
     * @return 验证结果
     */
    private ResponseResult validateDocumentQuantities(List<DocumentDetailVo> lists) {
        for (DocumentDetailVo documentDetail : lists) {
            // 查询单据明细信息
            BasicDocumentDetail basicDocumentDetail = basicDocumentDetailMapper.selectById(documentDetail.getId());
            if (basicDocumentDetail == null) {
                return ResponseResult.getErrorResult("单据明细不存在，ID: " + documentDetail.getId());
            }

            Integer currentNum = documentDetail.getCurrentNum();
            Integer quantity = basicDocumentDetail.getQuantity();
            Integer completedNum = basicDocumentDetail.getCompletedNum();

            // 验证本次数量必须大于0
            if (currentNum == null || currentNum <= 0) {
                return ResponseResult.getErrorResult("物料编码 " + documentDetail.getMaterialCode() +
                    " 的本次数量必须大于0，当前值: " + currentNum);
            }

            // 计算剩余可操作数量
            Integer remainingQuantity = quantity - completedNum;

            // 验证本次数量不能超过剩余可操作数量
            if (currentNum > remainingQuantity) {
                return ResponseResult.getErrorResult("物料编码 " + documentDetail.getMaterialCode() +
                    " 的本次数量 " + currentNum + " 超过了剩余可操作数量 " + remainingQuantity +
                    "（计划数量: " + quantity + "，已完成数量: " + completedNum + "）");
            }

            // 校验每个批次的状态
            List<DocumentInventoryDetailVo> details = documentDetail.getDetails();
            if (details != null) {
                for (DocumentInventoryDetailVo detailVo : details) {
                    DocumentInventoryDetail detail = documentInventoryDetailService.getById(detailVo.getId());
                    if (detail == null) {
                        return ResponseResult.getErrorResult("批次记录不存在，ID: " + detailVo.getId());
                    }

                    // 校验质检状态
                    if (!isQcStatusValid(detail.getQcStatus(), detail.getIsConcession())) {
                        return ResponseResult.getErrorResult("物料编码 " + documentDetail.getMaterialCode() +
                            " 批次ID " + detailVo.getId() + " 质检状态不允许出入库操作，当前状态：" +
                            getQcStatusDescription(detail.getQcStatus()) +
                            (detail.getIsConcession() != null && detail.getIsConcession().equals(CommonConstant.IsConcession.YES) ? "（已让步接收）" : ""));
                    }

                    // 校验出入库状态
                    if (!isWarehouseStatusValid(detail.getWarehouseStatus())) {
                        return ResponseResult.getErrorResult("物料编码 " + documentDetail.getMaterialCode() +
                            " 批次ID " + detailVo.getId() + " 出入库状态不允许操作，当前状态：" +
                            getWarehouseStatusDescription(detail.getWarehouseStatus()));
                    }
                }
            }
        }

        return ResponseResult.getSuccessResult();
    }

    /**
     * 根据入库/出库类型获取正确的容器编码
     * 入库：使用前端传递的容器编码（用户选择的目标容器）
     * 出库：从数据库记录中获取容器编码（已确定的源容器）
     */
    private String getContainerCodeByType(DocumentInventoryDetailVo detailVo, Integer type) {
        if (type == CommonConstant.InoutType.IN) {
            // 入库：使用前端传递的容器编码
            return detailVo.getContainerCode();
        } else {
            // 出库：从数据库记录中获取容器编码
            DocumentInventoryDetail existingDetail = documentInventoryDetailService.getById(detailVo.getId());
            return existingDetail != null ? existingDetail.getContainerCode() : null;
        }
    }

    /**
     * 获取确认操作的容器编码
     */
    private String getContainerCodeForConfirm(DocumentInventoryItemVo item, DocumentInventoryDetail detail, Integer type) {
        if (type == CommonConstant.InoutType.IN) {
            // 入库：使用前端传递的容器编码
            return item.getContainerCode();
        } else {
            // 出库：从数据库记录中获取容器编码
            return detail.getContainerCode();
        }
    }

    /**
     * 验证确认项数据
     */
    private ResponseResult validateConfirmItems(List<DocumentInventoryItemVo> items, Integer type) {
        for (DocumentInventoryItemVo item : items) {
            // 获取批次记录
            DocumentInventoryDetail detail = documentInventoryDetailService.getById(item.getDocumentInventoryDetailId());
            if (detail == null) {
                return ResponseResult.getErrorResult("批次记录不存在，ID: " + item.getDocumentInventoryDetailId());
            }

            // 校验质检状态
            if (!isQcStatusValid(detail.getQcStatus(), detail.getIsConcession())) {
                return ResponseResult.getErrorResult("批次ID " + item.getDocumentInventoryDetailId() +
                    " 质检状态不允许出入库操作，当前状态：" + getQcStatusDescription(detail.getQcStatus()) +
                    (detail.getIsConcession() != null && detail.getIsConcession().equals(CommonConstant.IsConcession.YES) ? "（已让步接收）" : ""));
            }

            // 校验出入库状态
            if (!isWarehouseStatusValid(detail.getWarehouseStatus())) {
                return ResponseResult.getErrorResult("批次ID " + item.getDocumentInventoryDetailId() + " 出入库状态不允许操作，当前状态：" + getWarehouseStatusDescription(detail.getWarehouseStatus()));
            }

            // 验证确认数量
            Integer confirmQuantity = item.getConfirmQuantity();
            Integer remainingQuantity = detail.getQuantity() - detail.getCompletedNum();

            if (confirmQuantity > remainingQuantity) {
                return ResponseResult.getErrorResult("批次ID " + item.getDocumentInventoryDetailId() + " 的确认数量 " + confirmQuantity + " 超过了剩余可操作数量 " + remainingQuantity);
            }

            // 入库时验证容器编码
            if (type == CommonConstant.InoutType.IN && (item.getContainerCode() == null || item.getContainerCode().trim().isEmpty())) {
                return ResponseResult.getErrorResult("入库操作必须指定目标容器编码");
            }
        }
        return ResponseResult.getSuccessResult();
    }

    /**
     * 创建出入库记录
     */
    private void createInOutRecord(DocumentInventoryItemVo item, DocumentInventoryDetail detail, String containerCode, List<RecordMaterialInout> inOutList) {
        // 获取单据信息
        BasicDocumentDetail documentDetail = basicDocumentDetailMapper.selectById(detail.getDetailCode());
        BasicDocumentInfo documentInfo = basicDocumentInfoMapper.selectById(documentDetail.getDocumentCode());
        RecordMaterialInout recordMaterialInout = new RecordMaterialInout();
        recordMaterialInout.setId(UUID.randomUUID().toString());
        recordMaterialInout.setInoutType(documentInfo.getTransactionType());
        recordMaterialInout.setDataOrigin(documentInfo.getBusinessSource());
        recordMaterialInout.setBoundType(documentInfo.getBusinessType());
        recordMaterialInout.setMaterialCode(detail.getMaterialCode());
        recordMaterialInout.setTotalNum(item.getConfirmQuantity());
        recordMaterialInout.setBoundIndex(documentInfo.getTransactionCode());
        recordMaterialInout.setRecordDate(DateAndTimeUtil.getNowDate());
        recordMaterialInout.setContainerCode(containerCode);
        recordMaterialInout.setRecorder(SecurityUtils.getUsername());
        recordMaterialInout.setLockTime(DateAndTimeUtil.getNowDate());
        recordMaterialInout.setUpperIndex(documentInfo.getSourceDocumentNo());
        recordMaterialInout.setBatch(detail.getBatchCode());
        recordMaterialInout.setProduceDate(new Date());
        inOutList.add(recordMaterialInout);
    }

    /**
     * 处理非立库确认
     */
    private void executeWithoutLkConfirm(List<DocumentInventoryItemVo> items, Integer type) {
        for (DocumentInventoryItemVo item : items) {
            DocumentInventoryDetail detail = documentInventoryDetailService.getById(item.getDocumentInventoryDetailId());
            if (type == CommonConstant.InoutType.IN) {
                // 入库：创建批次库存
                String targetContainerCode = item.getContainerCode();
                BasicWarehouseContainer container = basicWarehouseContainerMapper.getContainerByCode(targetContainerCode);
                if (container != null) {
                    // 创建批次库存
                    BasicMaterialBatchInventory batchInventory = new BasicMaterialBatchInventory();
                    batchInventory.setId(UUID.randomUUID().toString());
                    // 使用DocumentInventoryDetail中的批次号
                    batchInventory.setBatch(detail.getBatchCode());
                    batchInventory.setMaterialCode(detail.getMaterialCode());
                    batchInventory.setAvailNum(item.getConfirmQuantity());
                    batchInventory.setContainerCode(targetContainerCode);
                    batchInventory.setFreezeNum(0);
                    batchInventory.setCreateTime(new Date());
                    batchInventory.setProduceDate(new Date());
                    batchInventory.setInDate(new Date());
                    batchInventory.setUpperIndex(detail.getDocumentCode());
                    batchInventory.setMaterialNum(item.getConfirmQuantity());
                    materialBatchInventoryMapper.insert(batchInventory);
                    // 更新批次记录的容器信息
                    detail.setContainerCode(targetContainerCode);
                }
            } else {
                // 出库：扣减批次库存（根据容器、物料、批次号查询）
                BasicMaterialBatchInventory batchInventory = basicMaterialBatchInventoryService.getMaterialBatchInventory(
                        detail.getContainerCode(), detail.getBatchCode(), detail.getMaterialCode(), null);
                if (batchInventory != null) {
                    batchInventory.setFreezeNum(batchInventory.getFreezeNum() - item.getConfirmQuantity());
                    batchInventory.setMaterialNum(batchInventory.getMaterialNum() - item.getConfirmQuantity());
                    batchInventory.setAvailNum(batchInventory.getMaterialNum() - batchInventory.getFreezeNum());
                    materialBatchInventoryMapper.updateById(batchInventory);

                    // 库存为0时删除记录
                    if (batchInventory.getMaterialNum() == 0 && batchInventory.getAvailNum() == 0) {
                        materialBatchInventoryMapper.deleteById(batchInventory.getId());
                    }
                }
            }

            // 更新批次记录
            updateInventoryDetailStatus(detail, item.getConfirmQuantity());
        }
    }

    /**
     * 处理立库确认
     */
    private void executeLkConfirm(List<DocumentInventoryItemVo> items, String lkName, Integer type) {
        // 构建立库任务请求
        LkSendTaskRequest lkRequest = new LkSendTaskRequest();
        lkRequest.setId(UUID.randomUUID().toString());
        lkRequest.setTimestamp(new Date());

        List<LkSendTaskDetail> taskDetails = new ArrayList<>();
        for (DocumentInventoryItemVo item : items) {
            DocumentInventoryDetail detail = documentInventoryDetailService.getById(item.getDocumentInventoryDetailId());
            BasicDocumentDetail documentDetail = basicDocumentDetailMapper.selectById(detail.getDetailCode());

            LkSendTaskDetail taskDetail = new LkSendTaskDetail();
            taskDetail.setTaskNo(getLkTaskNo(documentDetail.getDocumentCode()));
            taskDetail.setSource("WMS");

            List<LkTaskMaterial> materials = new ArrayList<>();
            LkTaskMaterial material = new LkTaskMaterial();
            material.setMaterielCode(detail.getMaterialCode());
            material.setQuantity(item.getConfirmQuantity());
            materials.add(material);

            taskDetail.setMaterielInfos(materials);
            taskDetails.add(taskDetail);
        }
        lkRequest.setData(taskDetails);

        // 发送立库任务
        ResponseResult result;
        if (type == CommonConstant.InoutType.IN) {
            result = lkSystemService.sendLkInTask(lkRequest, lkName);
        } else {
            result = lkSystemService.sendLkOutTask(lkRequest, lkName);
        }

        // 处理成功后更新记录
        if (result != null && result.getCode().equals(ResultMsg.successCode)) {
            for (DocumentInventoryItemVo item : items) {
                DocumentInventoryDetail detail = documentInventoryDetailService.getById(item.getDocumentInventoryDetailId());

                // 入库时更新容器信息
                if (type == CommonConstant.InoutType.IN) {
                    detail.setContainerCode(item.getContainerCode());
                }

                // 更新批次记录状态
                updateInventoryDetailStatus(detail, item.getConfirmQuantity());
            }
        }
    }

    /**
     * 更新批次记录状态
     */
    private void updateInventoryDetailStatus(DocumentInventoryDetail detail, Integer confirmQuantity) {
        detail.setCompletedNum(detail.getCompletedNum() + confirmQuantity);

        // 检查是否完全完成
        if (detail.getCompletedNum().equals(detail.getQuantity())) {
            detail.setWarehouseStatus(CommonConstant.WarehouseStatus.COMPLETED);
            detail.setCompletedTime(new Date());
        }

        documentInventoryDetailService.updateById(detail);

        // 更新单据明细状态
        BasicDocumentDetail documentDetail = basicDocumentDetailMapper.selectById(detail.getDetailCode());
        documentDetail.setCompletedNum(documentDetail.getCompletedNum() + confirmQuantity);
        basicDocumentDetailMapper.updateById(documentDetail);

        // 更新最新批次状态
        basicDocumentInfoService.updateLatestBatchStatus(detail.getDetailCode());
    }

    /**
     * 根据批次项更新单据状态
     */
    private void updateDocumentStatusByItems(List<DocumentInventoryItemVo> items) {
        // 获取涉及的单据ID
        Set<String> documentIds = new HashSet<>();
        for (DocumentInventoryItemVo item : items) {
            DocumentInventoryDetail detail = documentInventoryDetailService.getById(item.getDocumentInventoryDetailId());
            BasicDocumentDetail documentDetail = basicDocumentDetailMapper.selectById(detail.getDetailCode());
            documentIds.add(documentDetail.getDocumentCode());
        }

        // 检查每个单据是否完成
        for (String documentId : documentIds) {
            BasicDocumentInfo documentInfo = basicDocumentInfoMapper.selectById(documentId);
            List<BasicDocumentDetail> details = basicDocumentDetailMapper.selectByDocumentCode(documentId);

            boolean allCompleted = true;
            for (BasicDocumentDetail detail : details) {
                if (!detail.getQuantity().equals(detail.getCompletedNum())) {
                    allCompleted = false;
                    break;
                }
            }

            if (allCompleted) {
                documentInfo.setStatus(CommonConstant.DocumentStatus.FINISH);
                basicDocumentInfoMapper.updateById(documentInfo);
            }
        }
    }

    /**
     * 根据批次项上报ERP
     */
    private void reportToErpByItems(List<DocumentInventoryItemVo> items, Integer type) {
        // 获取ERP来源的单据
        Set<String> erpDocumentIds = new HashSet<>();
        for (DocumentInventoryItemVo item : items) {
            DocumentInventoryDetail detail = documentInventoryDetailService.getById(item.getDocumentInventoryDetailId());
            BasicDocumentDetail documentDetail = basicDocumentDetailMapper.selectById(detail.getDetailCode());
            BasicDocumentInfo documentInfo = basicDocumentInfoMapper.selectById(documentDetail.getDocumentCode());

            if (documentInfo != null && CommonConstant.BusinessSource.ERP == documentInfo.getBusinessSource()) {
                erpDocumentIds.add(documentInfo.getId());
            }
        }

        if (!erpDocumentIds.isEmpty()) {
            // 构建DocumentDetailVo列表用于ERP上报
            List<DocumentDetailVo> erpLists = new ArrayList<>();
            for (String documentId : erpDocumentIds) {
                List<BasicDocumentDetail> details = basicDocumentDetailMapper.selectByDocumentCode(documentId);
                for (BasicDocumentDetail detail : details) {
                    DocumentDetailVo detailVo = new DocumentDetailVo();
                    detailVo.setId(detail.getId());
                    detailVo.setDocumentCode(detail.getDocumentCode());
                    detailVo.setMaterialCode(detail.getMaterialCode());
                    detailVo.setCurrentNum(detail.getCompletedNum());
                    erpLists.add(detailVo);
                }
            }
            erpReportService.reportCurrentOperation(erpLists, type);
        }
    }

    /**
     * 校验质检状态是否允许出入库
     * @param qcStatus 质检状态
     * @param isConcession 是否让步接收
     * @return true-允许出入库，false-不允许出入库
     */
    private boolean isQcStatusValid(Integer qcStatus, Integer isConcession) {
        if (qcStatus == null) {
            return false;
        }

        // 允许出入库的质检状态
        if (qcStatus.equals(CommonConstant.QcStatus.NO_NEED) ||      // 无需质检
            qcStatus.equals(CommonConstant.QcStatus.QUALIFIED) ||    // 质检合格
            qcStatus.equals(CommonConstant.QcStatus.EXEMPT)) {       // 免检
            return true;
        }

        // 质检不合格但让步接收的情况
        if (qcStatus.equals(CommonConstant.QcStatus.UNQUALIFIED) &&
            isConcession != null && isConcession.equals(CommonConstant.IsConcession.YES)) {
            return true;
        }

        return false;
    }

    /**
     * 校验出入库状态是否允许操作
     * @param warehouseStatus 出入库状态
     * @return true-允许操作，false-不允许操作
     */
    private boolean isWarehouseStatusValid(Integer warehouseStatus) {
        if (warehouseStatus == null) {
            return false;
        }

        // 只有待出入库状态才允许进行出入库操作
        return warehouseStatus.equals(CommonConstant.WarehouseStatus.PENDING_WAREHOUSE);
    }

    /**
     * 获取质检状态描述
     */
    private String getQcStatusDescription(Integer qcStatus) {
        if (qcStatus == null) return "未知";
        switch (qcStatus) {
            case 0: return "无需质检";
            case 1: return "待质检";
            case 2: return "质检中";
            case 3: return "质检合格";
            case 4: return "质检不合格";
            case 5: return "免检";
            default: return "未知状态";
        }
    }

    /**
     * 获取出入库状态描述
     */
    private String getWarehouseStatusDescription(Integer warehouseStatus) {
        if (warehouseStatus == null) return "未知";
        switch (warehouseStatus) {
            case 0: return "待确认";
            case 1: return "待出入库";
            case 2: return "出入库中";
            case 3: return "待生产确认";
            case 4: return "生产已确认";
            case 5: return "待仓库确认";
            case 6: return "仓库已确认";
            case 7: return "已完成";
            default: return "未知状态";
        }
    }
}
